# 🧠 Project Codex

**Project Codex** is an interactive chatbot interface built using [Next.js](https://nextjs.org) and [Tailwind CSS](https://tailwindcss.com).

---

## 🛠️ Getting Started

Make sure you create a `.env` file with your enviornment variables when running locally \
Set the environment variable `GEMINI_API_KEY` in `.env` to your Gemini API key \
Set `NEXT_PUBLIC_BASE_URL` in `.env` to the URL for the Codex API \
Set `NEXT_PUBLIC_STARTING_LANGUAGE` in `.env` to language Chat<PERSON> should start with

### 🐳 Running with Docker

---

> Make sure you have [Docker](https://www.docker.com/products/docker-desktop/) installed before running these commands.

To build and run the app using Docker:

```bash
# Build the Docker image
docker build -t codex-chatbot .

# Run the Docker container
docker run --env-file .env -p 3000:3000 codex-chatbot
```

Then visit [http://localhost:3000](http://localhost:3000) in your browser to access the app.


### 💻 Running locally without Docker

---

Install dependencies and start the development server:

```bash
npm install
npm run dev
```

Then open [http://localhost:3000](http://localhost:3000) in your browser to view the app.  
By default, the homepage redirects to `/chatbot`.

---

## 🗂️ Project Structure

```
/app
  /api             → Gemini API Interaction
  /chatbot         → Main chatbot page
  /components      → Components that make up the chatbot page
  /services        → Project Codex FastAPI Interactions
  globals.css      → Global tailwind and avatar CSS
  layout.tsx       → Root layout for HTML structure and theming
  page.tsx         → Redirects to /chatbot

/components/ui     → Code for react-avatar
/lib               → Tailwind utility functions
```

---

## 🧩 Architecture Overview

### Front-End (NextJS + App Router)

| Layer | Purpose |
|-------|---------|
| **Client Components** | `ChatbotInterface`, `ChatControls`, `ChatMessages`, `ChatInput` |
| **Route Handlers** | `/app/api/chat/route.ts` streams Gemini responses |
| **Styling** | TailwindCSS utility classes |

We take a finite state machine approach to the chatbot, as seen in the `ChatbotInterace` component.

--

## Whats Next?

- OCR Support: See our current development & testing via https://colab.research.google.com/drive/1O9XdBCSTmBy4fcYwkaoKduuPkKDLv00H?usp=sharing
- Switch to a vector database
- Batch translations
- More chat features like `/info`