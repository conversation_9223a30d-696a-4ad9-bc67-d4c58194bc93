"use client"

import { cn } from "@/lib/utils"
import { ChatAvatar } from "./ChatAvatar"
import { useEffect, useRef } from "react"

interface Message {
  content: string;
  isUser: boolean;
}

// Verified that only one message is being sent at a time
// User cannot send a message while the bot is generating a response

interface ChatMessagesProps {
  messages: Message[];
}

/**
* ChatMessages component
* Renders the list of chat messages
*/

export default function ChatMessages({ messages }: ChatMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  if (messages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full space-y-4">
        <div className="flex items-center justify-center h-16 w-16 rounded-full bg-indigo-100 animate-pulse">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
          </svg>
        </div>
        <div className="text-center space-y-2">
          <p className="text-indigo-500 bg-white py-2 px-4 rounded-full shadow-sm border border-indigo-100">Ready for your medical translation</p>
          <p className="text-xs text-indigo-400">Type a message to begin or continue your translation</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="flex flex-col space-y-8">
      {messages.map((msg, idx) => (
        <div
          key={idx}
          className={cn(
            "group flex w-full animate-fadeIn",
            msg.isUser ? "justify-end" : "justify-start"
          )}
          style={{ animationDelay: `${idx * 50}ms` }}
        >
          <div className={cn(
            "flex max-w-[85%] items-start gap-4",
            msg.isUser ? "flex-row-reverse" : "flex-row"
          )}>
            <div className="flex-shrink-0">
              <div className="flex flex-col items-center gap-1.5">
                <ChatAvatar isUser={msg.isUser} />
                <span className="text-xs font-medium bg-white px-2 py-0.5 rounded-full border shadow-sm" 
                  style={{ 
                    borderColor: msg.isUser ? 'rgba(79, 70, 229, 0.2)' : 'rgba(16, 185, 129, 0.2)',
                    color: msg.isUser ? '#4f46e5' : '#10b981'
                  }}>
                  {msg.isUser ? "You" : "Assistant"}
                </span>
              </div>
            </div>
            <div 
              className={cn(
                "flex min-w-0 flex-col gap-1.5",
                msg.isUser ? "items-end" : "items-start"
              )}
            >
              <div
                className={cn(
                  "rounded-2xl px-5 py-3.5 break-words shadow-sm",
                  msg.isUser
                    ? "bg-gradient-to-br from-indigo-500 to-indigo-600 text-white rounded-tr-none"
                    : "bg-white text-indigo-900 rounded-tl-none border border-indigo-100"
                )}
                style={{
                  boxShadow: msg.isUser 
                    ? '0 4px 6px -1px rgba(79, 70, 229, 0.1), 0 2px 4px -1px rgba(79, 70, 229, 0.06)' 
                    : '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)'
                }}
              >
                {msg.content.split("\n").map((line, i) => {
                  if (line.trim() === '') return <div key={i} className="h-2" />;
                  
                  const isListItem = /^\d+\.\s/.test(line);
                  
                  if (isListItem) {
                    return (
                      <div key={i} className={i > 0 ? "mt-1.5" : ""}>
                        <span className={msg.isUser ? "font-medium" : "font-medium text-indigo-700"}>
                          {line.split('. ')[0]}.
                        </span>
                        <span> {line.split('. ').slice(1).join('. ')}</span>
                      </div>
                    );
                  }
                  
                  return (
                    <p key={i} className={i > 0 ? "mt-2.5" : ""}>
                      {line}
                    </p>
                  );
                })}
              </div>
              <div className="flex items-center text-xs px-2" style={{ color: 'rgba(79, 70, 229, 0.6)' }}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          </div>
        </div>
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
}