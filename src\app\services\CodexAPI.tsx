// services/CodexAPI.tsx

// -------------------------
// Interfaces for API Payloads and Responses
// -------------------------

// Parameters for fuzzy match API (optional max_distance and max_results)
export interface FuzzyMatchParams {
  source_language: string;
  query: string;
  target_language: string;
  max_distance?: number;
  max_results?: number;
}

// Structure of the result returned by fuzzy match API
export interface FuzzyMatchResult {
  matching_name: string;
  matching_source: string;
  matching_algorithm: string;
  matching_uid: number;
  matching_row_number: number;
}

// Request payload for translation based on fuzzy match result
export interface TranslationRequest {
  translation_query: FuzzyMatchResult;
  target_language: string;
}

// Structure of the result returned by the translation API
export interface TranslationResult {
  translated_name: string;
  translated_source: string;
  translated_uid: number;
}

// Request payload for fallback translation (used when no fuzzy match is found)
export interface FallbackTranslationRequest {
  medicine: FuzzyMatchResult;
  target_language: string;
}

// Response structure from fallback translation endpoint
export interface FallbackTranslationResponse {
  translated_medicine: string;
}

// Request payload for submitting a manual translation
export interface ManualTranslationRequest {
  term: string;
  proposed_translation: string;
  source_language: string;
  target_language: string;
  description: string;
}

// Response structure from manual translation endpoint
export interface ManualTranslationResponse {
  translated_medicine: string;
}

// Response structure from the available languages API
export interface LanguageResponse {
  available_languages: { [key: string]: { target_languages: string[] } }
}

// -------------------------
// Actual API Calls
// -------------------------

import { BASE_URL } from '@/lib/utils';

// Calls fuzzy matching endpoint with optional distance/limit, returns a list of match results
export async function fetchFuzzyMatching(params: FuzzyMatchParams): Promise<FuzzyMatchResult[]> {
  const response = await fetch(`${BASE_URL}/fuzzymatching/`, {
    method: 'POST',
    headers: {
      accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      max_distance: 10, // default value
      max_results: 10,  // default value
      ...params,        // override defaults if provided
    }),
  });

  if (!response.ok) {
    throw new Error('Fuzzy Matching API failed');
  }

  const data = await response.json();
  return data.results;
}

// Translates a fuzzy-matched medicine into the target language
export async function translateMatch(request: TranslationRequest): Promise<TranslationResult[]> {
  const endpoint = '/translate/';
  const url = `${BASE_URL}${endpoint}`;
  const payload = JSON.stringify(request);

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: payload,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Translation API failed:
  Status: ${response.status} ${response.statusText}
  Endpoint: ${endpoint}
  Payload: ${payload}
  Response: ${errorText}`
      );
    }

    const data = await response.json();
    return data.results;
  } catch (err) {
    console.error('Error occurred during translation request:', err);
    throw err;
  }
}

// Provides a fallback translation when fuzzy match fails
export async function fallbackTranslate(request: FallbackTranslationRequest): Promise<FallbackTranslationResponse> {
  const endpoint = '/fallback_translation/';
  const url = `${BASE_URL}${endpoint}`;
  const payload = JSON.stringify(request);

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: payload,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Fallback Translation API failed:
  Status: ${response.status} ${response.statusText}
  Endpoint: ${endpoint}
  Payload: ${payload}
  Response: ${errorText}`
      );
    }

    const data = await response.json();
    return data;
  } catch (err) {
    console.error('Error during fallback translation request:', err);
    throw err;
  }
}

// Submits a manual translation entry for a medical term
export async function submitManualTranslation(request: ManualTranslationRequest): Promise<ManualTranslationResponse> {
  const endpoint = '/manual_translation/';
  const url = `${BASE_URL}${endpoint}`;
  const payload = JSON.stringify(request);

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: payload,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Manual Translation API failed:
  Status: ${response.status} ${response.statusText}
  Endpoint: ${endpoint}
  Payload: ${payload}
  Response: ${errorText}`
      );
    }

    const data = await response.json();
    return data;
  } catch (err) {
    console.error('Error during manual translation request:', err);
    throw err;
  }
}

// Fetches the list of available source/target languages
export async function fetchLanguages(): Promise<LanguageResponse> {
  const response = await fetch(`${BASE_URL}/languages/`, {
    method: 'GET',
    headers: {
      accept: 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Languages API failed');
  }

  const data = await response.json();
  return data;
}