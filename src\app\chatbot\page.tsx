'use client';

import dynamic from 'next/dynamic';

// Dynamically import ChatbotInterface to prevent SSR issues with Tolgee
const ChatbotInterface = dynamic(() => import('@/app/components/ChatbotInterface'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-[90vh]">
      <div className="flex flex-col items-center space-y-3">
        <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-indigo-100">
          <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-indigo-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          <span className="text-sm text-indigo-600 ml-2">Loading chatbot...</span>
        </div>
        <p className="text-xs text-indigo-400">Initializing Project Codex</p>
      </div>
    </div>
  )
});

export default function ChatbotPage() {
    return (
      <main className="flex items-center justify-center bg-[#f7f7f8] min-h-screen px-4 py-6">
        <div className="w-full max-w-3xl h-full">
          <ChatbotInterface />
        </div>
      </main>
    );
  }
  