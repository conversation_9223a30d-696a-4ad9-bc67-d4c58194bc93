"use client"

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar"
import { User } from "lucide-react"
import { cn } from "@/lib/utils"

interface ChatAvatarProps {
  isUser: boolean;
}

export function ChatAvatar({ isUser }: ChatAvatarProps) {
  return (
    <Avatar className={cn(
      "h-9 w-9 ring-2 ring-offset-2 ring-offset-white shadow-sm transition-all hover:scale-105",
      isUser ? "ring-indigo-200" : "ring-emerald-200"
    )}
    style={{
      boxShadow: isUser 
        ? '0 4px 6px -1px rgba(79, 70, 229, 0.1), 0 2px 4px -1px rgba(79, 70, 229, 0.06)'
        : '0 4px 6px -1px rgba(16, 185, 129, 0.1), 0 2px 4px -1px rgba(16, 185, 129, 0.06)'
    }}>
      <AvatarFallback 
        className={cn(
          "flex items-center justify-center",
          isUser 
            ? "bg-gradient-to-br from-indigo-50 to-indigo-100" 
            : "bg-gradient-to-br from-emerald-50 to-emerald-100"
        )}
      >
        {isUser ? (
          <User className="h-4 w-4 text-indigo-700" />
        ) : (
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-emerald-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
            </svg>
          </div>
        )}
      </AvatarFallback>
    </Avatar>
  );
} 