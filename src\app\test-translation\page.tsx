'use client';

import { useTranslation } from '@/lib/lang';
import { useState } from 'react';

export default function TestTranslationPage() {
  const { t, changeLanguage, getCurrentLanguage } = useTranslation();
  const [currentLang, setCurrentLang] = useState('en');

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'uk', name: 'Українська', flag: '🇺🇦' },
    { code: 'ru', name: 'Русский', flag: '🇷🇺' }
  ];

  const handleLanguageChange = (lang: string) => {
    setCurrentLang(lang);
    changeLanguage(lang);
  };

  const testKeys = [
    'startup_message',
    'target_prompt',
    'available_options',
    'translating',
    'source_prompt',
    'invalid_language'
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Translation System Test</h1>
        
        {/* Language Selector */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Language Selector Test</h2>
          <div className="flex space-x-4">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  currentLang === language.code
                    ? 'bg-indigo-600 text-white border-indigo-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <span className="mr-2">{language.flag}</span>
                {language.name}
              </button>
            ))}
          </div>
          <p className="mt-4 text-sm text-gray-600">
            Current language: <strong>{currentLang}</strong> (from hook: <strong>{getCurrentLanguage()}</strong>)
          </p>
        </div>

        {/* Translation Test */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Translation Test</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testKeys.map((key) => (
              <div key={key} className="border rounded p-3">
                <div className="text-sm font-medium text-gray-500 mb-1">{key}</div>
                <div className="text-gray-900">{t(key)}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">How to Test</h3>
          <ol className="list-decimal list-inside text-blue-800 space-y-1">
            <li>Click the language buttons above to switch languages</li>
            <li>Watch the translations change in real-time</li>
            <li>The current language should update immediately</li>
            <li>All text should change to the selected language</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
