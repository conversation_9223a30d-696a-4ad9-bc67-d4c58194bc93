{"name": "medical-codex-chatbot-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.0", "@radix-ui/react-avatar": "^1.1.3", "@tolgee/react": "^6.2.6", "@tolgee/web": "^6.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.486.0", "next": "15.2.3", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4.0.15", "typescript": "^5"}}