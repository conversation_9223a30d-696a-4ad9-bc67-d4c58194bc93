import './globals.css'
import TolgeeClientProvider from '@/components/TolgeeClientProvider';

export const metadata = {
  title: 'Codex – Medical Translation Assistant',
  description:
    '<PERSON> is a translation assistant designed to help translate medical terms between languages',
  keywords: [
    'Codex',
    'Medical Translation',
    'Healthcare Assistant'
  ],
  authors: [{ name: 'Grey-Box', url: 'https://www.grey-box.ca/' }],
  creator: 'Grey-Box',
  metadataBase: new URL('https://chatbot.asu2025.azure.project-codex.grey-box.ca'),
  openGraph: {
    title: 'Codex – Medical Translation Assistant',
    description:
      'Codex is a translation assistant designed to help translate medical terms between languages',
    url: 'https://chatbot.asu2025.azure.project-codex.grey-box.ca/',
    siteName: 'Codex',
    locale: 'en_US',
    type: 'website',
  }
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <TolgeeClientProvider>
          {children}
        </TolgeeClientProvider>
      </body>
    </html>
  );
}
