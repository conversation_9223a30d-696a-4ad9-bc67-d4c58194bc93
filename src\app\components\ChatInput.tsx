'use client';

import { useState } from 'react';

interface ChatInputProps {
  onSend: (message: string) => void;
  isLoading?: boolean;
}

export default function ChatInput({ onSend, isLoading = false }: ChatInputProps) {
  // Track whats currently being typed
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  // Handle sending a message
  const handleSend = () => {
    if (!inputValue.trim() || isLoading) return; // don't send empty string or if loading
    
    // Call send handler with input
    onSend(inputValue);

    // Clear the input box after sending
    setInputValue('');
  };

  //  handler for pressing enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSend();
    }
  };

  return (
    <div className={`flex items-center gap-3 bg-white rounded-full p-1.5 pr-2 shadow-sm border transition-all ${
      isFocused 
        ? 'border-indigo-300 shadow-md ring-2 ring-indigo-100' 
        : 'border-indigo-100 hover:border-indigo-200'
    }`}>
      <div className="flex-shrink-0 pl-3">
        <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 transition-colors ${
          isFocused ? 'text-indigo-500' : 'text-indigo-400'
        }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
        </svg>
      </div>
      {/* input box */}
      <input
        type="text"
        placeholder="Type your medical term to translate..."
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        disabled={isLoading}
        className="flex-grow px-3 py-2.5 rounded-full focus:outline-none bg-transparent disabled:text-gray-400 placeholder-indigo-300"
      />

      <button
        onClick={handleSend}
        disabled={isLoading || !inputValue.trim()}
        aria-label="Send message"
        title="Send message"
        className={`px-4 py-2.5 rounded-full text-white transition-all flex items-center justify-center group ${
          isLoading || !inputValue.trim() 
            ? 'bg-indigo-300 cursor-not-allowed' 
            : 'bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 shadow-sm'
        }`}
      >
        {isLoading ? (
          <span className="flex items-center">
            <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
        ) : (
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 transform group-hover:translate-x-0.5 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </span>
        )}
      </button>
    </div>
  );
}