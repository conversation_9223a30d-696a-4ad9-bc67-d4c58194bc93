import { Tolgee, DevTools, FormatSimple } from "@tolgee/web";
import { TolgeeProvider, useTolgee } from "@tolgee/react";

// Create Tolgee instance
export const tolgee = Tolgee()
  .use(DevTools())
  .use(FormatSimple())
  .init({
    apiUrl: process.env.NEXT_PUBLIC_TOLGEE_API_URL,
    apiKey: process.env.NEXT_PUBLIC_TOLGEE_API_KEY,
    projectId: process.env.NEXT_PUBLIC_TOLGEE_PROJECT_ID,
    defaultLanguage: 'en',
    availableLanguages: ['en', 'uk', 'ru'],
  });

// Export hook for easy access
export { useTolgee };